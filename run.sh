#!/bin/bash

# SQL Formatter Runner Script
# This script sets up the environment and runs the SQL formatter

set -e  # Exit on any error

echo "============================================================"
echo "SQL FORMATTER - FANFIX VIEW DEFINITIONS"
echo "============================================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Error: Python is not installed or not in PATH"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Using Python: $(which $PYTHON_CMD)"

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "📦 Virtual environment found. Activating..."
    source venv/bin/activate
    echo "✅ Virtual environment activated"
else
    echo "⚠️  No virtual environment found. Using system Python."
    echo "💡 Consider creating one with: python3 -m venv venv"
fi

# Check if required packages are installed
echo "🔍 Checking required packages..."

if ! $PYTHON_CMD -c "import snowflake.connector" 2>/dev/null; then
    echo "❌ snowflake-connector-python not found"
    echo "📦 Installing required packages..."
    $PYTHON_CMD -m pip install snowflake-connector-python cryptography
else
    echo "✅ snowflake-connector-python is installed"
fi

if ! $PYTHON_CMD -c "import cryptography" 2>/dev/null; then
    echo "❌ cryptography not found"
    echo "📦 Installing cryptography..."
    $PYTHON_CMD -m pip install cryptography
else
    echo "✅ cryptography is installed"
fi

# Optional: Check for python-dotenv
if ! $PYTHON_CMD -c "import dotenv" 2>/dev/null; then
    echo "💡 python-dotenv not found (optional). Installing..."
    $PYTHON_CMD -m pip install python-dotenv
else
    echo "✅ python-dotenv is installed"
fi

# Check if the main script exists
if [ ! -f "update_view_definitions.py" ]; then
    echo "❌ Error: update_view_definitions.py not found in current directory"
    echo "📁 Current directory: $(pwd)"
    echo "📋 Files in directory:"
    ls -la
    exit 1
fi

echo ""
echo "🚀 Starting SQL formatter..."
echo "============================================================"

# Run the Python script
$PYTHON_CMD update_view_definitions.py

# Check exit status
if [ $? -eq 0 ]; then
    echo ""
    echo "============================================================"
    echo "✅ SQL formatter completed successfully!"
    echo "📁 Check fanfix_view_prod.sql for the formatted output"
    echo "============================================================"
else
    echo ""
    echo "============================================================"
    echo "❌ SQL formatter failed!"
    echo "============================================================"
    exit 1
fi