# SQL Formatter for Snowflake Views

A Python script that automatically pulls view definitions from Snow<PERSON><PERSON>'s FANFIX_VIEW_PROD schema, formats them according to SQL best practices, and generates a consolidated SQL file with properly ordered view definitions.

## Overview

This tool connects to <PERSON><PERSON><PERSON>, retrieves all view definitions from the `FANFIX_VIEW_PROD.ANALYTICS` schema, applies comprehensive SQL formatting, analyzes view dependencies, and outputs a single SQL file (`fanfix_view_prod.sql`) containing all views in the correct dependency order.

## Features

### 🔄 **Automated View Retrieval**
- Connects to <PERSON>f<PERSON> using private key authentication
- Queries all views in the `FANFIX_VIEW_PROD.ANALYTICS` schema
- Retrieves complete view definitions from `INFORMATION_SCHEMA.VIEWS`

### 🎨 **Advanced SQL Formatting**
- **Keyword Capitalization**: Automatically capitalizes SQL keywords (SELECT, FROM, WHERE, etc.)
- **Column Formatting**: Places each SELECT column on its own line with proper indentation
- **JOIN Clause Formatting**: Properly formats all JOIN types with line breaks
- **CASE Statement Formatting**: Structures CASE/WHEN/THEN/ELSE/END with proper indentation
- **CTE Formatting**: Formats Common Table Expressions with proper nesting
- **Function Call Preservation**: Keeps single-line functions intact (TO_DATE, DATEADD, etc.)
- **Operator Spacing**: Normalizes spacing around operators (=, <>, >=, etc.)
- **AND/OR Clause Indentation**: Properly indents logical operators with tabs

### 📊 **Dependency Analysis**
- Analyzes view dependencies by scanning for references to other views
- Performs topological sorting to order views correctly
- Prevents dependency errors when recreating views
- Handles circular dependencies gracefully

### 🛡️ **Safety Features**
- Creates timestamped backups before overwriting existing files
- Comprehensive error handling and logging
- Environment variable support for secure credential management
- Optional `.env` file support for local development

## Prerequisites

### Required Python Packages
```bash
pip install snowflake-connector-python cryptography
```

### Optional Packages
```bash
pip install python-dotenv  # For .env file support
```

## Configuration

### Environment Variables
Set the following environment variables or create a `.env` file:

```bash
# Required
SNOWFLAKE_ACCOUNT=your_account_identifier
SNOWFLAKE_USER=your_username
SNOWFLAKE_PRIVATE_KEY_PATH=/path/to/your/private_key.p8

# Optional
SNOWFLAKE_ROLE=your_role
SNOWFLAKE_WAREHOUSE=FANFIX_ENGINEERING
SNOWFLAKE_DATABASE=your_database
SNOWFLAKE_SCHEMA=your_schema
SNOWFLAKE_PRIVATE_KEY_PASSPHRASE=your_passphrase
```

### Authentication
The script uses Snowflake's private key authentication. Ensure you have:
1. A valid private key file (`.p8` format)
2. The private key configured in your Snowflake user account
3. Appropriate permissions to read from `FANFIX_VIEW_PROD.ANALYTICS` schema

## Usage

### Quick Start
1. **Set up environment variables** (see Configuration section above)
2. **Install dependencies**: `pip install snowflake-connector-python cryptography`
3. **Run the script**: `python update_view_definitions.py`

### Detailed Run Instructions

#### Option 1: Direct Execution
```bash
# Navigate to the project directory
cd /path/to/sql-formatter

# Install required packages
pip install snowflake-connector-python cryptography

# Optional: Install dotenv for .env file support
pip install python-dotenv

# Run the script
python update_view_definitions.py
```

#### Option 2: Using Virtual Environment (Recommended)
```bash
# Navigate to the project directory
cd /path/to/sql-formatter

# Create virtual environment (if not exists)
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # On macOS/Linux
# or
venv\Scripts\activate     # On Windows

# Install dependencies
pip install -r requirements.txt
# or manually:
pip install snowflake-connector-python cryptography python-dotenv

# Run the script
python update_view_definitions.py

# Deactivate virtual environment when done
deactivate
```

#### Option 3: Using the Shell Script (if available)
```bash
# Make the script executable
chmod +x run.sh

# Run the shell script
./run.sh
```

### Pre-Run Checklist
Before running the script, ensure:
- [ ] Snowflake credentials are properly configured
- [ ] Private key file exists and is accessible
- [ ] Required Python packages are installed
- [ ] You have read access to `FANFIX_VIEW_PROD.ANALYTICS` schema
- [ ] Write permissions in the current directory for output file

### Expected Output
When the script runs successfully, you'll see:
```
============================================================
FANFIX VIEW DEFINITION UPDATE SCRIPT
============================================================
Starting view definition update process...

Step 1: Retrieving view definitions from Snowflake...
Connecting to Snowflake...
Found 303 views in FANFIX_VIEW_PROD.ANALYTICS schema
✅ Successfully retrieved 303 view definitions

Step 2: Analyzing view dependencies...
Analyzing view dependencies...
Ordered 303 views by dependencies
✅ Successfully ordered views by dependencies

Step 3: Generating formatted SQL file...
Generating SQL file: fanfix_view_prod.sql
Processing view: ACTIVE_USERS_1DAY
Processing view: ACTIVE_USERS_28DAY
...
✅ Successfully generated fanfix_view_prod.sql

============================================================
✅ VIEW DEFINITION UPDATE COMPLETED SUCCESSFULLY!
============================================================
📁 Output file: fanfix_view_prod.sql
📊 Total views processed: 303
🕒 Completed at: 2025-08-07 10:01:14
```

## Output

The script generates `fanfix_view_prod.sql` with the following structure:

```sql
----
CREATE SCHEMA IF NOT EXISTS FANFIX_VIEW_PROD.ANALYTICS
;
----

-- ========================================
-- VIEWS ORDERED BY DEPENDENCIES
-- Schema: FANFIX_VIEW_PROD.ANALYTICS
-- Generated automatically on 2025-08-07 10:01:14
-- Total views: 303
-- ========================================

CREATE OR REPLACE VIEW FANFIX_VIEW_PROD.ANALYTICS.VIEW_NAME
 AS
SELECT
    column1,
    column2,
    CASE
        WHEN condition1 THEN value1
        WHEN condition2 THEN value2
        ELSE default_value
    END AS calculated_column
FROM source_table
WHERE condition = 'value'
    AND another_condition IS NOT NULL
;
```

## SQL Formatting Rules

### Column Formatting
- Each SELECT column on its own line
- Consistent 4-space indentation for columns
- Proper comma placement (trailing commas)

### CASE Statements
- `CASE` keyword on new line with tab indentation
- `WHEN` conditions with double-tab indentation
- `THEN` values on same line as `WHEN`
- `ELSE` clause with double-tab indentation
- `END` keyword with tab indentation

### JOIN Clauses
- Each JOIN on its own line
- Proper capitalization of JOIN types
- Consistent formatting for all JOIN variants

### Logical Operators
- `AND`/`OR` operators with tab indentation
- Proper line breaks for complex conditions
- Context-aware indentation for nested conditions

## File Structure

```
sql-formatter/
├── update_view_definitions.py  # Main script
├── fanfix_view_prod.sql       # Generated output file
├── requirements.txt           # Python dependencies
├── run.sh                     # Shell script (if needed)
├── venv/                      # Virtual environment
└── README.md                  # This file
```

## Error Handling

The script includes comprehensive error handling for:
- Missing environment variables
- Snowflake connection failures
- Private key authentication issues
- File I/O operations
- SQL parsing and formatting errors

## Backup and Recovery

- Automatic backup creation with timestamp: `fanfix_view_prod.sql.backup.YYYYMMDD_HHMMSS`
- Original files preserved before any modifications
- Easy rollback capability

## Logging and Monitoring

The script provides detailed console output including:
- Connection status
- Number of views retrieved
- Dependency analysis results
- Formatting progress
- Success/failure notifications
- Execution timestamps

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install snowflake-connector-python cryptography
   ```

2. **Authentication Errors**
   - Verify private key path and permissions
   - Check Snowflake user configuration
   - Ensure private key is properly formatted

3. **Permission Errors**
   - Verify access to `FANFIX_VIEW_PROD.ANALYTICS` schema
   - Check warehouse and role permissions

4. **Environment Variables**
   - Ensure all required variables are set
   - Check `.env` file format and location

## Contributing

When modifying the script:
1. Test with a small subset of views first
2. Verify SQL formatting output manually
3. Check dependency ordering logic
4. Ensure backward compatibility

## License

This script is designed for internal use with the FANFIX Snowflake environment.